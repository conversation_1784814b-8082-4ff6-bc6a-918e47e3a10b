import torch
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_percentage_error
from arguments import get_args


class ModelEvaluator:
    """
    Unified evaluator for all model types.
    Provides consistent evaluation metrics across different models.
    """

    def __init__(self):
        pass

    def _judge_correct(self, pred, label, args):
        """Calculate tolerance-based accuracy."""
        if isinstance(pred, torch.Tensor):
            pred = pred.detach().cpu().numpy()
        if isinstance(label, torch.Tensor):
            label = label.detach().cpu().numpy()

        # Calculate relative error
        diff = np.abs(pred - label)
        power_correct = np.where(np.abs(diff[:, 0] / label[:, 0]) < args.epsilon_power, 1, 0).sum()
        area_correct = np.where(np.abs(diff[:, 1] / label[:, 1]) < args.epsilon_area, 1, 0).sum()

        # Convert to percentage
        total_samples = len(pred)
        power_correct = power_correct / total_samples
        area_correct = area_correct / total_samples

        return power_correct, area_correct

    def calculate_metrics(self, y_true, y_pred, args):
        """
        Calculate comprehensive evaluation metrics.

        Args:
            y_true: True labels (numpy array or torch tensor)
            y_pred: Predicted labels (numpy array or torch tensor)
            args: Command line arguments

        Returns:
            dict: Dictionary containing all metrics
        """
        # Convert to numpy arrays if needed
        if isinstance(y_true, torch.Tensor):
            y_true = y_true.detach().cpu().numpy()
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()

        # Calculate MSE for each target and overall
        power_mse = mean_squared_error(y_true[:, 0], y_pred[:, 0])
        area_mse = mean_squared_error(y_true[:, 1], y_pred[:, 1])
        overall_mse = mean_squared_error(y_true, y_pred)

        # Calculate RMSE
        power_rmse = np.sqrt(power_mse)
        area_rmse = np.sqrt(area_mse)
        overall_rmse = np.sqrt(overall_mse)

        # Calculate R² scores
        power_r2 = r2_score(y_true[:, 0], y_pred[:, 0])
        area_r2 = r2_score(y_true[:, 1], y_pred[:, 1])
        overall_r2 = r2_score(y_true, y_pred)

        # Calculate MAPE (Mean Absolute Percentage Error)
        # Handle potential division by zero
        power_mape = mean_absolute_percentage_error(y_true[:, 0], y_pred[:, 0])
        area_mape = mean_absolute_percentage_error(y_true[:, 1], y_pred[:, 1])
        overall_mape = mean_absolute_percentage_error(y_true.flatten(), y_pred.flatten())

        # Calculate tolerance-based accuracy
        power_correct, area_correct = self._judge_correct(y_pred, y_true, args)

        metrics = {
            'power_mse': power_mse,
            'area_mse': area_mse,
            'overall_mse': overall_mse,
            'power_rmse': power_rmse,
            'area_rmse': area_rmse,
            'overall_rmse': overall_rmse,
            'power_r2': power_r2,
            'area_r2': area_r2,
            'overall_r2': overall_r2,
            'power_mape': power_mape,
            'area_mape': area_mape,
            'overall_mape': overall_mape,
            'power_accuracy': power_correct,
            'area_accuracy': area_correct
        }

        return metrics

    def evaluate_pytorch_model(self, dataloader, model, loss_fn, device='cpu'):
        """
        Evaluate PyTorch model on given dataloader.

        Args:
            dataloader: Test data loader
            model: PyTorch model
            loss_fn: Loss function
            device: Device to run evaluation on

        Returns:
            dict: Dictionary containing all metrics
        """
        args = get_args()
        size = len(dataloader.dataset)
        num_batches = len(dataloader)
        model.eval()
        test_loss = 0

        # Collect all predictions and labels
        all_preds = []
        all_labels = []

        with torch.no_grad():
            for embedding, label in dataloader:
                embedding, label = embedding.to(device), label.to(device)
                pred = model(embedding)
                test_loss += loss_fn(pred, label).item()

                # Store predictions and labels
                all_preds.append(pred.cpu().numpy())
                all_labels.append(label.cpu().numpy())

        # Concatenate all predictions and labels
        all_preds = np.concatenate(all_preds, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)

        # Calculate comprehensive metrics
        metrics = self.calculate_metrics(all_labels, all_preds, args)

        # Add loss information
        metrics['test_loss'] = test_loss / num_batches

        return metrics

    def evaluate_sklearn_model(self, X_test, y_test, model, args):
        """
        Evaluate sklearn/XGBoost model.

        Args:
            X_test: Test features
            y_test: Test labels
            model: Trained model with predict method
            args: Command line arguments

        Returns:
            dict: Dictionary containing all metrics
        """
        # Make predictions
        y_pred = model.predict(X_test)

        # Convert to numpy if needed
        if isinstance(y_pred, torch.Tensor):
            y_pred = y_pred.detach().cpu().numpy()

        # Calculate comprehensive metrics
        metrics = self.calculate_metrics(y_test, y_pred, args)

        return metrics

    def print_metrics(self, metrics, model_name="Model"):
        """Print metrics in a formatted way."""
        print(f"\n{'='*60}")
        print(f"{model_name.upper()} EVALUATION METRICS")
        print('='*60)

        # Handle test_loss which might not exist for some models
        test_loss = metrics.get('test_loss', None)
        if test_loss is not None:
            print(f"Loss: {test_loss:.6f}")

        print(f"\nMSE Metrics:")
        print(f"  Power MSE:   {metrics['power_mse']:.6f}")
        print(f"  Area MSE:    {metrics['area_mse']:.6f}")
        print(f"  Overall MSE: {metrics['overall_mse']:.6f}")

        print(f"\nRMSE Metrics:")
        print(f"  Power RMSE:   {metrics['power_rmse']:.6f}")
        print(f"  Area RMSE:    {metrics['area_rmse']:.6f}")
        print(f"  Overall RMSE: {metrics['overall_rmse']:.6f}")

        print(f"\nR² Metrics:")
        print(f"  Power R²:   {metrics['power_r2']:.6f}")
        print(f"  Area R²:    {metrics['area_r2']:.6f}")
        print(f"  Overall R²: {metrics['overall_r2']:.6f}")

        print(f"\nMAPE Metrics:")
        print(f"  Power MAPE:   {metrics['power_mape']:.6f}")
        print(f"  Area MAPE:    {metrics['area_mape']:.6f}")
        print(f"  Overall MAPE: {metrics['overall_mape']:.6f}")

        print(f"\nTolerance-based Accuracy:")
        print(f"  Power Accuracy: {metrics['power_accuracy']:.4f}")
        print(f"  Area Accuracy:  {metrics['area_accuracy']:.4f}")

        print('='*60)


# Legacy functions for backward compatibility
def judge_correct(pred, label):
    """Legacy function for backward compatibility."""
    args = get_args()
    evaluator = ModelEvaluator()
    return evaluator._judge_correct(pred, label, args)


def test(dataloader, model, loss_fn, device='cpu'):
    """Legacy function for backward compatibility."""
    evaluator = ModelEvaluator()
    metrics = evaluator.evaluate_pytorch_model(dataloader, model, loss_fn, device)

    # Return in old format
    return metrics['test_loss'], metrics['power_accuracy'], metrics['area_accuracy']


def test_with_metrics(dataloader, model, loss_fn, device='cpu'):
    """Enhanced test function that calculates all metrics."""
    evaluator = ModelEvaluator()
    return evaluator.evaluate_pytorch_model(dataloader, model, loss_fn, device)

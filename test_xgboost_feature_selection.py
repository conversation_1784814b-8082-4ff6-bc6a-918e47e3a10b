#!/usr/bin/env python3
"""
Test XGBoost with different numbers of top correlated features.
This script tests XGBoost performance using top 5, 10, 15, and 20 features
based on Pearson correlation with power and area labels.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
import xgboost as xgb
from sklearn.metrics import mean_squared_error, r2_score
import os
import warnings
warnings.filterwarnings('ignore')

class FeatureSelectionXGBoostTester:
    def __init__(self, csv_path='./contest.csv', test_fold=3, kfold=5, random_seed=42, shuffle_data=True):
        """
        Initialize the tester with dataset parameters.

        Args:
            csv_path: Path to the contest.csv file
            test_fold: Which fold to use for testing (0 to kfold-1)
            kfold: Number of folds for cross-validation
            random_seed: Random seed for reproducibility
            shuffle_data: Whether to shuffle data before splitting
        """
        self.csv_path = csv_path
        self.test_fold = test_fold
        self.kfold = kfold
        self.random_seed = random_seed
        self.shuffle_data = shuffle_data
        self.features = None
        self.power = None
        self.area = None
        self.X_train = None
        self.y_train = None
        self.X_test = None
        self.y_test = None
        self.feature_correlations = None
        self.results = {}

    def load_and_parse_data(self):
        """Load and parse the contest.csv file."""
        print("Loading dataset...")
        dataset = pd.read_csv(self.csv_path)
        dataset = dataset.to_numpy()

        # Parse the data (assuming first column contains space-separated values)
        data = []
        for row in dataset:
            data.append(np.array((row[0]).split()).astype('float32'))

        data = np.array(data)

        # Extract features (first 29 columns) and labels (columns 30-31: power and area)
        self.features = data[:, :29]
        self.power = data[:, 30]  # Column 30 is power
        self.area = data[:, 31]   # Column 31 is area

        print(f"Dataset shape: {data.shape}")
        print(f"Features shape: {self.features.shape}")
        print(f"Power shape: {self.power.shape}")
        print(f"Area shape: {self.area.shape}")

    def split_data(self):
        """Split data into train/test sets using k-fold logic."""
        # Shuffle data if requested
        if self.shuffle_data:
            # Set random seed for reproducibility
            np.random.seed(self.random_seed)

            # Create shuffled indices
            indices = np.arange(len(self.features))
            np.random.shuffle(indices)

            # Apply shuffling
            self.features = self.features[indices]
            self.power = self.power[indices]
            self.area = self.area[indices]

            print(f"Feature selection data shuffled with random seed: {self.random_seed}")
        else:
            print("Feature selection data not shuffled")

        dataset_length = len(self.features)
        test_data_start = dataset_length // self.kfold * self.test_fold
        test_data_end = dataset_length // self.kfold * (self.test_fold + 1)

        # Create train/test indices
        test_indices = list(range(test_data_start, test_data_end))
        train_indices = list(range(0, test_data_start)) + list(range(test_data_end, dataset_length))

        # Split features
        self.X_train = self.features[train_indices]
        self.X_test = self.features[test_indices]

        # Split labels (combine power and area)
        labels = np.column_stack([self.power, self.area])
        self.y_train = labels[train_indices]
        self.y_test = labels[test_indices]

        # Normalize labels (same as in data_processing.py)
        self.y_train = self.normalize_labels(self.y_train)
        self.y_test = self.normalize_labels(self.y_test)

        print(f"Train set: {self.X_train.shape[0]} samples")
        print(f"Test set: {self.X_test.shape[0]} samples")

    def normalize_labels(self, labels):
        """Normalize labels using the same logic as data_processing.py."""
        labels = np.array(labels)
        for i in range(labels.shape[1]):
            mean_val = np.mean(labels[:, i])
            std_val = np.std(labels[:, i])
            labels[:, i] = (labels[:, i] - mean_val) / std_val
        return labels

    def calculate_feature_correlations(self):
        """Calculate correlations between features and both power and area."""
        print("\nCalculating feature correlations...")

        n_features = self.features.shape[1]
        correlations = []

        for i in range(n_features):
            feature = self.features[:, i]

            # Calculate correlations with power and area
            power_corr, _ = pearsonr(feature, self.power)
            area_corr, _ = pearsonr(feature, self.area)

            # Use average absolute correlation as the ranking metric
            avg_abs_corr = (abs(power_corr) + abs(area_corr)) / 2

            correlations.append({
                'feature_index': i,
                'power_corr': power_corr,
                'area_corr': area_corr,
                'avg_abs_corr': avg_abs_corr
            })

        self.feature_correlations = pd.DataFrame(correlations)
        self.feature_correlations = self.feature_correlations.sort_values('avg_abs_corr', ascending=False)

        print("Top 10 features by average absolute correlation:")
        print(self.feature_correlations.head(10)[['feature_index', 'power_corr', 'area_corr', 'avg_abs_corr']])

    def test_xgboost_with_top_features(self, top_n_features):
        """Test XGBoost with top N features."""
        print(f"\n{'='*50}")
        print(f"Testing XGBoost with top {top_n_features} features")
        print(f"{'='*50}")

        # Get top N feature indices
        top_features = self.feature_correlations.head(top_n_features)['feature_index'].values
        print(f"Selected features: {top_features}")

        # Select only top features
        X_train_selected = self.X_train[:, top_features]
        X_test_selected = self.X_test[:, top_features]

        # XGBoost parameters (similar to xgboost_utils.py)
        xgb_params = {
            'objective': 'reg:squarederror',
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 1.0,
            'colsample_bytree': 1.0,
            'reg_alpha': 0.0,
            'reg_lambda': 1.0,
            'random_state': 42,
            'verbosity': 0
        }

        # Train separate models for power and area
        results = {}

        for target_idx, target_name in enumerate(['power', 'area']):
            print(f"\nTraining XGBoost for {target_name} prediction...")

            # Prepare data
            dtrain = xgb.DMatrix(X_train_selected, label=self.y_train[:, target_idx])
            dtest = xgb.DMatrix(X_test_selected, label=self.y_test[:, target_idx])

            # Train model
            model = xgb.train(
                params=xgb_params,
                dtrain=dtrain,
                num_boost_round=100,
                evals=[(dtrain, 'train'), (dtest, 'test')],
                verbose_eval=False
            )

            # Make predictions
            train_pred = model.predict(dtrain)
            test_pred = model.predict(dtest)

            # Calculate metrics
            train_mse = mean_squared_error(self.y_train[:, target_idx], train_pred)
            test_mse = mean_squared_error(self.y_test[:, target_idx], test_pred)
            train_r2 = r2_score(self.y_train[:, target_idx], train_pred)
            test_r2 = r2_score(self.y_test[:, target_idx], test_pred)

            # Calculate accuracy (same logic as evaluate.py)
            epsilon = 0.1  # Default epsilon value
            diff = np.abs(test_pred - self.y_test[:, target_idx])
            accuracy = np.sum(np.abs(diff / self.y_test[:, target_idx]) < epsilon) / len(self.y_test)

            results[target_name] = {
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'accuracy': accuracy,
                'model': model
            }

            print(f"{target_name.capitalize()} - Train MSE: {train_mse:.4f}, Test MSE: {test_mse:.4f}")
            print(f"{target_name.capitalize()} - Train R²: {train_r2:.4f}, Test R²: {test_r2:.4f}")
            print(f"{target_name.capitalize()} - Accuracy: {accuracy:.4f}")

        # Store results
        self.results[top_n_features] = {
            'selected_features': top_features,
            'power': results['power'],
            'area': results['area']
        }

        return results

    def run_all_tests(self):
        """Run tests for all feature counts (5, 10, 15, 20)."""
        print("Starting XGBoost Feature Selection Tests")
        print("=" * 60)

        # Load and prepare data
        self.load_and_parse_data()
        self.split_data()
        self.calculate_feature_correlations()

        # Test different numbers of features
        feature_counts = [5, 10, 15, 20]

        for n_features in feature_counts:
            if n_features <= len(self.feature_correlations):
                self.test_xgboost_with_top_features(n_features)
            else:
                print(f"Skipping {n_features} features (only {len(self.feature_correlations)} available)")

        # Generate summary and plots
        self.generate_summary()
        self.plot_results()

    def generate_summary(self):
        """Generate a summary of all test results."""
        print(f"\n{'='*60}")
        print("SUMMARY OF RESULTS")
        print(f"{'='*60}")

        summary_data = []

        for n_features, results in self.results.items():
            summary_data.append({
                'n_features': n_features,
                'power_test_mse': results['power']['test_mse'],
                'power_test_r2': results['power']['test_r2'],
                'power_accuracy': results['power']['accuracy'],
                'area_test_mse': results['area']['test_mse'],
                'area_test_r2': results['area']['test_r2'],
                'area_accuracy': results['area']['accuracy']
            })

        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False, float_format='%.4f'))

        # Save summary to CSV
        summary_df.to_csv('xgboost_feature_selection_summary.csv', index=False)
        print(f"\nSummary saved to 'xgboost_feature_selection_summary.csv'")

        # Find best performing configuration
        best_power_mse = summary_df.loc[summary_df['power_test_mse'].idxmin()]
        best_area_mse = summary_df.loc[summary_df['area_test_mse'].idxmin()]

        print(f"\nBest Power Prediction: {int(best_power_mse['n_features'])} features "
              f"(MSE: {best_power_mse['power_test_mse']:.4f})")
        print(f"Best Area Prediction: {int(best_area_mse['n_features'])} features "
              f"(MSE: {best_area_mse['area_test_mse']:.4f})")

    def plot_results(self):
        """Create visualization plots for the results."""
        if not self.results:
            print("No results to plot")
            return

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('XGBoost Performance vs Number of Features', fontsize=16)

        feature_counts = list(self.results.keys())

        # Extract metrics
        power_test_mse = [self.results[n]['power']['test_mse'] for n in feature_counts]
        power_test_r2 = [self.results[n]['power']['test_r2'] for n in feature_counts]
        power_accuracy = [self.results[n]['power']['accuracy'] for n in feature_counts]

        area_test_mse = [self.results[n]['area']['test_mse'] for n in feature_counts]
        area_test_r2 = [self.results[n]['area']['test_r2'] for n in feature_counts]
        area_accuracy = [self.results[n]['area']['accuracy'] for n in feature_counts]

        # Plot Power metrics
        axes[0, 0].plot(feature_counts, power_test_mse, 'bo-', linewidth=2, markersize=8)
        axes[0, 0].set_title('Power Prediction - Test MSE')
        axes[0, 0].set_xlabel('Number of Features')
        axes[0, 0].set_ylabel('MSE')
        axes[0, 0].grid(True, alpha=0.3)

        axes[0, 1].plot(feature_counts, power_test_r2, 'go-', linewidth=2, markersize=8)
        axes[0, 1].set_title('Power Prediction - Test R²')
        axes[0, 1].set_xlabel('Number of Features')
        axes[0, 1].set_ylabel('R²')
        axes[0, 1].grid(True, alpha=0.3)

        axes[0, 2].plot(feature_counts, power_accuracy, 'ro-', linewidth=2, markersize=8)
        axes[0, 2].set_title('Power Prediction - Accuracy')
        axes[0, 2].set_xlabel('Number of Features')
        axes[0, 2].set_ylabel('Accuracy')
        axes[0, 2].grid(True, alpha=0.3)

        # Plot Area metrics
        axes[1, 0].plot(feature_counts, area_test_mse, 'bo-', linewidth=2, markersize=8)
        axes[1, 0].set_title('Area Prediction - Test MSE')
        axes[1, 0].set_xlabel('Number of Features')
        axes[1, 0].set_ylabel('MSE')
        axes[1, 0].grid(True, alpha=0.3)

        axes[1, 1].plot(feature_counts, area_test_r2, 'go-', linewidth=2, markersize=8)
        axes[1, 1].set_title('Area Prediction - Test R²')
        axes[1, 1].set_xlabel('Number of Features')
        axes[1, 1].set_ylabel('R²')
        axes[1, 1].grid(True, alpha=0.3)

        axes[1, 2].plot(feature_counts, area_accuracy, 'ro-', linewidth=2, markersize=8)
        axes[1, 2].set_title('Area Prediction - Accuracy')
        axes[1, 2].set_xlabel('Number of Features')
        axes[1, 2].set_ylabel('Accuracy')
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('xgboost_feature_selection_results.png', dpi=300, bbox_inches='tight')
        print(f"Results plot saved as 'xgboost_feature_selection_results.png'")
        plt.show()

def main():
    """Main function to run the feature selection tests."""
    # Create tester instance
    tester = FeatureSelectionXGBoostTester(
        csv_path='./contest.csv',
        test_fold=3,
        kfold=5
    )

    # Run all tests
    tester.run_all_tests()

if __name__ == "__main__":
    main()

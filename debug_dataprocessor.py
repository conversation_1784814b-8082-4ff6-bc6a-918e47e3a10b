#!/usr/bin/env python3

from arguments import parse_args
from data_processing import DataProcessor

# Test the DataProcessor initialization
args = parse_args()
print(f"args.apply_scaling: {args.apply_scaling}")
print(f"args.apply_feature_engineering: {args.apply_feature_engineering}")

try:
    data_processor = DataProcessor(
        apply_feature_engineering=args.apply_feature_engineering,
        apply_feature_scaling=args.apply_scaling,
        apply_label_scaling=True
    )
    print("DataProcessor created successfully!")
except Exception as e:
    print(f"Error creating DataProcessor: {e}")
    print(f"Error type: {type(e)}")

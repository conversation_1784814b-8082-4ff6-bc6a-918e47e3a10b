import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, Dataset
import torch
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.utils import shuffle


class CustomTensorDataset(Dataset):
    def __init__(self, embeddings, labels):
        self.embeddings = embeddings
        self.labels = labels

    def __len__(self):
        return len(self.embeddings)

    def __getitem__(self, idx):
        embedding = torch.tensor(self.embeddings[idx])
        label = torch.tensor(self.labels[idx])
        return embedding, label


class DataProcessor:
    """
    Unified data processor for all model types.
    Handles feature engineering, scaling, and data splitting.
    """

    def __init__(self, apply_feature_engineering=True, apply_feature_scaling=True, apply_label_scaling=True):
        self.apply_feature_engineering = apply_feature_engineering
        self.apply_feature_scaling = apply_feature_scaling
        self.apply_label_scaling = apply_label_scaling
        self.feature_transformer = None
        self.label_scaler = None
        self.is_fitted = False

    def _load_raw_data(self):
        """Load and parse the contest.csv file."""
        dataset = pd.read_csv('./contest.csv')
        dataset = dataset.to_numpy()

        data = []
        for row in dataset:
            data.append(np.array((row[0]).split()).astype('float32'))

        return np.array(data)

    def _extract_features_and_labels(self, data):
        """Extract features and labels from raw data."""
        # First 29 columns are features, columns 30-31 are power and area
        features = data[:, :29]
        labels = data[:, 30:32]  # power and area

        return features, labels

    def _apply_feature_engineering(self, features):
        """Apply feature engineering including one-hot encoding for replacement policy."""
        if not self.apply_feature_engineering:
            return features

        # Column 25 (index 25) is replacement policy - apply one-hot encoding
        # Other columns get standard scaling
        if self.feature_transformer is None:
            # Create transformer for one-hot encoding replacement policy and scaling others
            categorical_features = [25]  # Replacement policy column
            numerical_features = [i for i in range(29) if i != 25]

            self.feature_transformer = ColumnTransformer(
                transformers=[
                    ('cat', OneHotEncoder(drop='first', sparse_output=False), categorical_features),
                    ('num', StandardScaler() if self.apply_feature_scaling else 'passthrough', numerical_features)
                ],
                remainder='passthrough'
            )

            # Fit the transformer
            features_transformed = self.feature_transformer.fit_transform(features)
        else:
            # Transform using fitted transformer
            features_transformed = self.feature_transformer.transform(features)

        return features_transformed

    def _normalize_labels(self, labels):
        """Normalize labels using standardization."""
        if not self.apply_label_scaling:
            return labels

        if self.label_scaler is None:
            self.label_scaler = StandardScaler()
            labels_normalized = self.label_scaler.fit_transform(labels)
        else:
            labels_normalized = self.label_scaler.transform(labels)

        return labels_normalized

    def _shuffle_data(self, features, labels, args):
        """Shuffle data with random seed for reproducibility."""
        if hasattr(args, 'shuffle_data') and args.shuffle_data:
            # Set random seed for reproducibility
            random_seed = getattr(args, 'random_seed', 42)
            np.random.seed(random_seed)

            # Create shuffled indices
            indices = np.arange(len(features))
            np.random.shuffle(indices)

            # Apply shuffling
            features_shuffled = features[indices]
            labels_shuffled = labels[indices]

            print(f"Data shuffled with random seed: {random_seed}")
            return features_shuffled, labels_shuffled
        else:
            print("Data not shuffled (shuffle_data=False or not specified)")
            return features, labels

    def _split_data(self, features, labels, args):
        """Split data into train and test sets using k-fold validation."""
        # First shuffle the data if requested
        features, labels = self._shuffle_data(features, labels, args)

        dataset_length = len(features)
        kfold = args.kfold
        test_fold = args.test_fold

        test_data_start = dataset_length // kfold * test_fold
        test_data_end = dataset_length // kfold * (test_fold + 1)

        # Create boolean mask for test data
        test_mask = np.zeros(dataset_length, dtype=bool)
        test_mask[test_data_start:test_data_end] = True

        # Split data
        X_train = features[~test_mask]
        y_train = labels[~test_mask]
        X_test = features[test_mask]
        y_test = labels[test_mask]

        return X_train, y_train, X_test, y_test

    def process_data(self, args, return_format='numpy'):
        """
        Main method to process data for any model type.

        Args:
            args: Command line arguments
            return_format: 'numpy' for sklearn/xgboost, 'dataloader' for pytorch

        Returns:
            Processed data in requested format
        """
        # Load raw data
        data = self._load_raw_data()

        # Extract features and labels
        features, labels = self._extract_features_and_labels(data)

        # Apply feature engineering
        features = self._apply_feature_engineering(features)

        # Normalize labels
        labels = self._normalize_labels(labels)

        # Split data
        X_train, y_train, X_test, y_test = self._split_data(features, labels, args)

        self.is_fitted = True

        if return_format == 'numpy':
            return X_train, y_train, X_test, y_test
        elif return_format == 'dataloader':
            # Convert to PyTorch DataLoaders
            train_dataset = CustomTensorDataset(X_train.tolist(), y_train.tolist())
            test_dataset = CustomTensorDataset(X_test.tolist(), y_test.tolist())

            train_dataloader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=False)
            test_dataloader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)

            return train_dataloader, test_dataloader
        else:
            raise ValueError(f"Unsupported return format: {return_format}")


# Legacy function for backward compatibility
def normalize_labels(labels):
    """Legacy function for backward compatibility."""
    labels = np.array(labels)
    for i in range(len(labels[0, :])):
        labels[:, i] = (labels[:, i] - np.mean(labels[:, i])) / np.std(labels[:, i])
    return labels.tolist()


def create_dataloaders(args):
    """Legacy function for backward compatibility."""
    processor = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=True, apply_label_scaling=True)
    return processor.process_data(args, return_format='dataloader')
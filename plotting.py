import matplotlib.pyplot as plt
import numpy as np


def plot_losses(train_losses, test_losses, nepochs, args):

    plt.figure()

    # Handle models that train in one go (XGBoost, Linear Regression, Gaussian Process)
    if args.model_name in ['xgboost', 'linear_regression', 'gaussian_process']:
        # For single-shot models, show the final loss as a bar chart
        categories = ['Train Loss', 'Test Loss']
        values = [train_losses[0], test_losses[0]]
        plt.bar(categories, values)
        model_display_name = args.model_name.replace('_', ' ').title()
        plt.title(f'{model_display_name} Final Losses, {args.test_fold} / {args.kfold} part data as test')
        plt.ylabel('Loss value')
    else:
        # For other models, show loss curves over epochs
        x = np.arange(nepochs)
        plt.plot(x, train_losses, label='train loss')
        plt.plot(x, test_losses, label=f'test loss')
        plt.title(f'Training and Testing Losses, {args.test_fold} / {args.kfold} part data as test')
        plt.xlabel('Epoch Idx')
        plt.ylabel('Loss value')
        plt.legend()

    plt.savefig(f'./plots/losses_plot_{args.model_name}_{args.test_fold}.png')


def plot_test_acc(test_acc, nepochs, args):

    plt.figure()

    # Handle models that train in one go (XGBoost, Linear Regression, Gaussian Process)
    if args.model_name in ['xgboost', 'linear_regression', 'gaussian_process']:
        # For single-shot models, show the final accuracy as a bar chart
        categories = ['Power Accuracy', 'Area Accuracy']
        values = [test_acc[0][0], test_acc[1][0]]
        plt.bar(categories, values)
        model_display_name = args.model_name.replace('_', ' ').title()
        plt.title(f'{model_display_name} Final Accuracy, deviating {args.epsilon_area *100 }%')
        plt.ylabel('Accuracy')
        plt.ylim(0, 1)  # Accuracy is between 0 and 1
    else:
        # For other models, show accuracy curves over epochs
        x = np.arange(nepochs)
        plt.plot(x, test_acc[0], label='power acc')
        plt.plot(x, test_acc[1], label=f'area acc')
        plt.title(f'Testing acc, deviating {args.epsilon_area *100 }%')
        plt.xlabel('Epoch Idx')
        plt.ylabel('Acc')
        plt.legend()

    plt.savefig(f'./plots/acc_plot_{args.model_name}_{args.test_fold}.png')


def plot_enhanced_metrics(metrics_dict, args):
    """
    Plot enhanced metrics including MSE and R² scores.

    Args:
        metrics_dict: Dictionary containing metrics from test_with_metrics
        args: Command line arguments
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle(f'{args.model_name.replace("_", " ").title()} - Enhanced Metrics', fontsize=16)

    # MSE plot
    mse_categories = ['Power MSE', 'Area MSE', 'Overall MSE']
    mse_values = [metrics_dict['power_mse'], metrics_dict['area_mse'], metrics_dict['overall_mse']]
    axes[0, 0].bar(mse_categories, mse_values, color=['blue', 'orange', 'green'])
    axes[0, 0].set_title('Mean Squared Error (MSE)')
    axes[0, 0].set_ylabel('MSE Value')
    axes[0, 0].tick_params(axis='x', rotation=45)

    # R² plot
    r2_categories = ['Power R²', 'Area R²', 'Overall R²']
    r2_values = [metrics_dict['power_r2'], metrics_dict['area_r2'], metrics_dict['overall_r2']]
    axes[0, 1].bar(r2_categories, r2_values, color=['blue', 'orange', 'green'])
    axes[0, 1].set_title('R² Score (Coefficient of Determination)')
    axes[0, 1].set_ylabel('R² Value')
    axes[0, 1].set_ylim(-1, 1)  # R² can be negative
    axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    axes[0, 1].tick_params(axis='x', rotation=45)

    # Accuracy plot
    acc_categories = ['Power Accuracy', 'Area Accuracy']
    acc_values = [metrics_dict.get('power_accuracy', 0), metrics_dict.get('area_accuracy', 0)]
    axes[1, 0].bar(acc_categories, acc_values, color=['blue', 'orange'])
    axes[1, 0].set_title(f'Tolerance-based Accuracy (±{args.epsilon_power*100:.1f}%)')
    axes[1, 0].set_ylabel('Accuracy')
    axes[1, 0].set_ylim(0, 1)

    # Combined metrics comparison
    metrics_names = ['MSE\n(lower better)', 'R²\n(higher better)', 'Accuracy\n(higher better)']
    power_metrics = [metrics_dict['power_mse'], metrics_dict['power_r2'], metrics_dict.get('power_accuracy', 0)]
    area_metrics = [metrics_dict['area_mse'], metrics_dict['area_r2'], metrics_dict.get('area_accuracy', 0)]

    x = np.arange(len(metrics_names))
    width = 0.35

    # Normalize MSE for comparison (invert and scale)
    power_metrics_norm = [1/(1+power_metrics[0]), power_metrics[1], power_metrics[2]]
    area_metrics_norm = [1/(1+area_metrics[0]), area_metrics[1], area_metrics[2]]

    axes[1, 1].bar(x - width/2, power_metrics_norm, width, label='Power', color='blue', alpha=0.7)
    axes[1, 1].bar(x + width/2, area_metrics_norm, width, label='Area', color='orange', alpha=0.7)
    axes[1, 1].set_title('Normalized Metrics Comparison')
    axes[1, 1].set_ylabel('Normalized Score')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(metrics_names)
    axes[1, 1].legend()
    axes[1, 1].set_ylim(0, 1)

    plt.tight_layout()
    plt.savefig(f'./plots/enhanced_metrics_{args.model_name}_{args.test_fold}.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Enhanced metrics plot saved as './plots/enhanced_metrics_{args.model_name}_{args.test_fold}.png'")
#!/usr/bin/env python3
"""
Test script to verify that data shuffling is working correctly with reproducibility.
"""

import numpy as np
from data_processing import DataProcessor

def test_shuffling_reproducibility():
    """Test that shuffling produces the same results with the same random seed."""
    print("Testing data shuffling reproducibility...")

    # Create mock arguments object
    class MockArgs:
        def __init__(self):
            self.shuffle_data = True
            self.random_seed = 42
            self.kfold = 5
            self.test_fold = 0
            self.apply_feature_engineering = False
            self.apply_scaling = False

    args = MockArgs()

    # Test 1: Same seed should produce same results
    print("\nTest 1: Same seed should produce same shuffled order")

    processor1 = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
    X_train1, y_train1, X_test1, y_test1 = processor1.process_data(args, return_format='numpy')

    processor2 = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
    X_train2, y_train2, X_test2, y_test2 = processor2.process_data(args, return_format='numpy')

    # Check if results are identical
    train_identical = np.array_equal(X_train1, X_train2) and np.array_equal(y_train1, y_train2)
    test_identical = np.array_equal(X_test1, X_test2) and np.array_equal(y_test1, y_test2)

    print(f"Training data identical: {train_identical}")
    print(f"Test data identical: {test_identical}")

    if train_identical and test_identical:
        print("✓ PASS: Same seed produces identical results")
    else:
        print("✗ FAIL: Same seed produces different results")

    # Test 2: Different seeds should produce different results
    print("\nTest 2: Different seeds should produce different shuffled order")

    args.random_seed = 123  # Different seed
    processor3 = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
    X_train3, y_train3, X_test3, y_test3 = processor3.process_data(args, return_format='numpy')

    # Check if results are different
    train_different = not np.array_equal(X_train1, X_train3) or not np.array_equal(y_train1, y_train3)
    test_different = not np.array_equal(X_test1, X_test3) or not np.array_equal(y_test1, y_test3)

    print(f"Training data different: {train_different}")
    print(f"Test data different: {test_different}")

    if train_different or test_different:
        print("✓ PASS: Different seeds produce different results")
    else:
        print("✗ FAIL: Different seeds produce identical results")

    # Test 3: No shuffling should produce original order
    print("\nTest 3: No shuffling should produce deterministic order")

    args.shuffle_data = False
    processor4 = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
    X_train4, y_train4, X_test4, y_test4 = processor4.process_data(args, return_format='numpy')

    processor5 = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
    X_train5, y_train5, X_test5, y_test5 = processor5.process_data(args, return_format='numpy')

    # Check if no-shuffle results are identical
    no_shuffle_identical = (np.array_equal(X_train4, X_train5) and np.array_equal(y_train4, y_train5) and
                           np.array_equal(X_test4, X_test5) and np.array_equal(y_test4, y_test5))

    print(f"No-shuffle results identical: {no_shuffle_identical}")

    if no_shuffle_identical:
        print("✓ PASS: No shuffling produces consistent results")
    else:
        print("✗ FAIL: No shuffling produces inconsistent results")

    print("\n" + "="*60)
    print("SHUFFLING TEST SUMMARY")
    print("="*60)
    print(f"✓ Reproducibility with same seed: {train_identical and test_identical}")
    print(f"✓ Different results with different seeds: {train_different or test_different}")
    print(f"✓ Consistency without shuffling: {no_shuffle_identical}")

    all_tests_passed = (train_identical and test_identical and
                       (train_different or test_different) and
                       no_shuffle_identical)

    if all_tests_passed:
        print("\n🎉 ALL TESTS PASSED! Shuffling is working correctly with reproducibility.")
    else:
        print("\n❌ SOME TESTS FAILED! Please check the shuffling implementation.")

    return all_tests_passed

def test_cross_fold_consistency():
    """Test that different folds with same seed produce consistent but different splits."""
    print("\n" + "="*60)
    print("TESTING CROSS-FOLD CONSISTENCY")
    print("="*60)

    # Create mock arguments object
    class MockArgs:
        def __init__(self):
            self.shuffle_data = True
            self.random_seed = 42
            self.kfold = 5
            self.apply_feature_engineering = False
            self.apply_scaling = False
            self.test_fold = 0  # Will be updated in loop

    args = MockArgs()

    fold_data = []

    for fold in range(5):
        args.test_fold = fold
        processor = DataProcessor(apply_feature_engineering=False, apply_feature_scaling=False, apply_label_scaling=False)
        X_train, y_train, X_test, y_test = processor.process_data(args, return_format='numpy')
        fold_data.append((X_train, y_train, X_test, y_test))
        print(f"Fold {fold}: Train size {len(X_train)}, Test size {len(X_test)}")

    # Check that test sets are non-overlapping
    print("\nChecking test set overlap...")
    for i in range(5):
        for j in range(i+1, 5):
            # Compare first few samples to check if test sets overlap
            overlap = np.array_equal(fold_data[i][2][:5], fold_data[j][2][:5])
            print(f"Fold {i} vs Fold {j} test overlap: {overlap}")

    print("Cross-fold consistency test completed.")

if __name__ == "__main__":
    test_shuffling_reproducibility()
    test_cross_fold_consistency()

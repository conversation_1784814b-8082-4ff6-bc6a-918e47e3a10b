import argparse

_ARGS = None

def get_args():
    return _ARGS

def set_args(args):
    global _ARGS
    _ARGS = args


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--epochs', type=int, default=50)
    parser.add_argument('--batch-size', type=int, default=128)
    parser.add_argument('--kfold', type=int, default=5)
    parser.add_argument('--test-fold', type=int, default=3, help='which part is used for validation, range from 0 to kfold - 1')
    parser.add_argument('--lr', type=float, default=1e-5)
    parser.add_argument('--weight-decay', type=float, default=0.01)
    parser.add_argument('--embed-dim', type=int, default=64)
    parser.add_argument('--epsilon-power', type=float, default=1e-1)
    parser.add_argument('--epsilon-area', type=float, default=1e-1)
    parser.add_argument('--nlayers', type=int, default=4)
    parser.add_argument('--nheads', type=int, default=4)
    parser.add_argument('--model-name', type=str, default='mlp',
                       choices=['mlp', 'unet', 'transformer', 'cnn', 'resnet', 'tabnet',
                               'xgboost', 'lightgbm',
                               'linear_regression', 'ridge', 'gaussian_process',
                               'knn', 'svr', 'random_forest'])

    # XGBoost specific parameters
    parser.add_argument('--xgb-n-estimators', type=int, default=100, help='Number of boosting rounds for XGBoost')
    parser.add_argument('--xgb-max-depth', type=int, default=6, help='Maximum depth of XGBoost trees')
    parser.add_argument('--xgb-learning-rate', type=float, default=0.1, help='Learning rate for XGBoost')
    parser.add_argument('--xgb-subsample', type=float, default=1.0, help='Subsample ratio for XGBoost')
    parser.add_argument('--xgb-colsample-bytree', type=float, default=1.0, help='Column subsample ratio for XGBoost')
    parser.add_argument('--xgb-reg-alpha', type=float, default=0.0, help='L1 regularization for XGBoost')
    parser.add_argument('--xgb-reg-lambda', type=float, default=1.0, help='L2 regularization for XGBoost')

    # Gaussian Process specific parameters
    parser.add_argument('--gp-kernel', type=str, default='rbf', choices=['rbf', 'matern', 'rational_quadratic'], help='Kernel for Gaussian Process')
    parser.add_argument('--gp-alpha', type=float, default=1e-10, help='Noise level for Gaussian Process')
    parser.add_argument('--gp-normalize-y', action='store_true', help='Normalize target values for Gaussian Process')

    # Ridge Regression parameters
    parser.add_argument('--ridge-alpha', type=float, default=1.0, help='Regularization strength for Ridge regression')

    # KNN parameters
    parser.add_argument('--knn-neighbors', type=int, default=5, help='Number of neighbors for KNN')
    parser.add_argument('--knn-weights', type=str, default='uniform', choices=['uniform', 'distance'], help='Weight function for KNN')
    parser.add_argument('--knn-algorithm', type=str, default='auto', choices=['auto', 'ball_tree', 'kd_tree', 'brute'], help='Algorithm for KNN')

    # SVR parameters
    parser.add_argument('--svr-kernel', type=str, default='rbf', choices=['linear', 'poly', 'rbf', 'sigmoid'], help='Kernel for SVR')
    parser.add_argument('--svr-C', type=float, default=1.0, help='Regularization parameter for SVR')
    parser.add_argument('--svr-gamma', type=str, default='scale', help='Kernel coefficient for SVR')
    parser.add_argument('--svr-epsilon', type=float, default=0.1, help='Epsilon parameter for SVR')

    # Random Forest parameters
    parser.add_argument('--rf-n-estimators', type=int, default=100, help='Number of trees in Random Forest')
    parser.add_argument('--rf-max-depth', type=int, default=None, help='Maximum depth of Random Forest trees')
    parser.add_argument('--rf-min-samples-split', type=int, default=2, help='Minimum samples required to split a node')
    parser.add_argument('--rf-min-samples-leaf', type=int, default=1, help='Minimum samples required at a leaf node')

    # Data processing parameters
    parser.add_argument('--apply-feature-engineering', action='store_true', help='Apply feature engineering (one-hot encoding, scaling)')
    parser.add_argument('--apply-scaling', action='store_true', default=False, help='Apply feature scaling')
    parser.add_argument('--random-seed', type=int, default=42, help='Random seed for reproducibility')
    parser.add_argument('--shuffle-data', action='store_true', default=True, help='Shuffle data before k-fold splitting')

    args = parser.parse_args()
    set_args(args)

    return args

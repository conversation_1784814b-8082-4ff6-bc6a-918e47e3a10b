#!/usr/bin/env python3
"""
Demonstration script showing how to use the new data shuffling functionality.
"""

import sys
from main_refactored import run_cross_validation

def demo_with_shuffling():
    """Demonstrate cross-validation with data shuffling enabled."""
    print("="*80)
    print("DEMO: Cross-validation with DATA SHUFFLING ENABLED")
    print("="*80)
    print("Random seed: 42 (for reproducibility)")
    print("Data will be shuffled before k-fold splitting")
    print()
    
    # Run XGBoost with shuffling enabled
    run_cross_validation('xgboost', args_override={
        'random_seed': 42,
        'shuffle_data': True,
        'apply_scaling': False,
        'apply_feature_engineering': False
    })

def demo_without_shuffling():
    """Demonstrate cross-validation with data shuffling disabled."""
    print("\n" + "="*80)
    print("DEMO: Cross-validation with DATA SHUFFLING DISABLED")
    print("="*80)
    print("Data will NOT be shuffled (original sequential order)")
    print()
    
    # Run XGBoost without shuffling
    run_cross_validation('xgboost', args_override={
        'random_seed': 42,
        'shuffle_data': False,
        'apply_scaling': False,
        'apply_feature_engineering': False
    })

def demo_different_seeds():
    """Demonstrate how different random seeds affect results."""
    print("\n" + "="*80)
    print("DEMO: Different random seeds with shuffling")
    print("="*80)
    
    seeds = [42, 123, 999]
    
    for seed in seeds:
        print(f"\n--- Running with random seed: {seed} ---")
        run_cross_validation('linear_regression', args_override={
            'random_seed': seed,
            'shuffle_data': True,
            'apply_scaling': True,
            'apply_feature_engineering': False
        })

if __name__ == "__main__":
    if len(sys.argv) > 1:
        demo_type = sys.argv[1]
        if demo_type == "with_shuffle":
            demo_with_shuffling()
        elif demo_type == "without_shuffle":
            demo_without_shuffling()
        elif demo_type == "different_seeds":
            demo_different_seeds()
        else:
            print("Usage: python demo_shuffling.py [with_shuffle|without_shuffle|different_seeds]")
    else:
        print("Available demos:")
        print("1. python demo_shuffling.py with_shuffle")
        print("2. python demo_shuffling.py without_shuffle") 
        print("3. python demo_shuffling.py different_seeds")
        print()
        print("Running default demo with shuffling...")
        demo_with_shuffling()
